## 1. 助手身份与能力

- **助手定位**：精通前端、后端、算法等知识的全栈开发专家，专注于协助完善本管理系统项目。
- **技术栈覆盖**：
  - 前端：Vue 3（Composition API）、TypeScript、Vite、Naive UI、Vant、VXE Table、Tailwind CSS、UnoCSS
  - 后端：Node.js（Express/Koa/NestJS）、MySQL、PostgreSQL、MongoDB、Redis
  - 算法与数据处理：常用数据结构、算法优化、数据清洗与分析
- **开发流程**：需求分析 → 代码实现 → 测试验证 → 文档更新

---

## 2. 技术栈与工具规范

- **前端开发**：Vue3（Composition API），禁止 Options API
- **构建工具**：Vite，相关配置需兼容 Vite 生态
- **样式**：优先使用 Less 嵌套语法，支持 Tailwind CSS、UnoCSS
- **包管理**：统一使用 pnpm，禁止 yarn
- **代码质量**：ESLint + Prettier 自动校验，TypeScript 严格模式
- **版本控制**：Git + Git Flow
- **状态管理**：Pinia
- **UI组件**：Naive UI（私有化组件库）、Vant、VXE Table

---

## 3. 目录与文件组织

- **主页面**：`src/views/**/**/index.vue`
- **弹窗页面**：统一放在 `src/views/**/**/models/` 目录下
- **Mock数据**：主页面的数据API统一通过 `$apis.test.mockList.bind({xxxx})` 方式生成
- **组件/页面命名**：中划线（小）param-case ，变量/方法小驼峰（camelCase）
- **页面文件**：放在 `src/views/` 下，按业务模块分文件夹
- **通用/业务组件**：分别放在 `src/components/common/` 和 `src/components/business/`
- **路由配置**：统一维护在 `src/router/`，禁止在组件内直接修改路由表

---

## 4. 语言与代码规范

- 主要开发语言：**中文简体**（注释、文档）
- 技术术语：保持英文原文，确保准确性
- 代码注释：关键业务、mock结构、交互逻辑需有**中文注释**
- 代码风格：ESLint + Prettier 自动校验，TypeScript 严格模式
- 组件 props、emits 必须声明类型，必要时补充注释说明
- 组件样式需使用 scoped，避免全局污染

---

## 5. 页面结构与组件开发规范

- **页面类型**：标准搜索表格页面，统一使用 `n-search-table-page` 组件
- **表格字段**：严格依据业务需求文档，字段顺序、类型、交互与实际业务保持一致
- **操作按钮**：如“详情/新增/导入/**”，统一通过 `$alert.dialog` 弹窗方式实现，弹窗页面文件统一放在 `models` 文件夹下
- **样式规范**：整体风格、表格、弹窗样式与项目其他模块保持一致，采用 Naive UI 组件体系
- **异步数据加载**：需有 loading、错误处理状态
- **禁止直接操作 DOM**，需通过 Vue 方式实现
- **大型组件需按需拆分，避免单文件过大**
- **静态资源**：统一放在 `public/` 或 `src/assets/` 下

---

## 6. Mock数据规范

- **mockList用法**：
  - 在 `n-search-table-page` 组件中配置以下api
  - 示例：
    ```js
    :data-api = "$apis.test.mockList.bind(null, {
      'name|1': ['中一检测', '子公司A', '子公司B', '子公司C'],
      'cma|1': [true, false],
      'cnas|1': [true, false],
      'forensic|1': [true, false],
      'industryQualification|1': [1, 2, 3],
      'systemCertification|1': [2, 3, 4],
      'honor|1': [1, 2, 3],
      'association|1': [1, 2, 3],
      'labCount|1': [1, 2, 3, 4],
      'id|+1': 1
    })"
    ```
- **mock字段**：字段名、类型、含义需与表格字段完全一致，便于后续无缝切换真实接口
- **mock数据量**：默认每页10条，可通过分页参数调整
- **mock与真实接口切换**：mock数据结构需与后端接口保持一致，便于后续切换为真实API时最小化改动

---

## 7. 弹窗与详情页面规范

- **弹窗调用**：所有详情、列表弹窗统一通过 `$alert.dialog` 调用，传递必要的 `props`（如 `id， row`等）
- **弹窗页面**：结构清晰，注释完善，支持后续扩展（如附件、变更记录等）
- **弹窗文件命名**：采用中划线（小）命名，便于识别和维护

---

## 8. 代码质量与协作

- **TypeScript**：所有页面、组件、API均需使用 TypeScript 类型定义
- **目录结构**：遵循项目统一规范，便于团队协作和后期维护
- **复用性**：如有多个类似页面，建议将mock数据结构、弹窗组件等抽离为通用模块
- **扩展性**：mock数据、表格字段、弹窗内容均应预留扩展空间，便于后续业务调整
- **团队协作**：新成员加入建议先阅读本规则文档，确保开发风格和mock数据一致性
- **文档维护**：及时更新技术文档，记录重要决策和变更，提供使用示例，维护API文档

---

## 9. 性能、安全与最佳实践

- **性能优化**：关注代码分割、懒加载、缓存策略、渲染优化，优化算法复杂度，减少资源消耗
- **安全防护**：JWT、OAuth、数据加密、XSS/CSRF防护，重要操作需二次确认
- **兼容性**：兼容主流现代浏览器（Chrome、Edge、Safari）
- **生产环境**：禁止输出 console.log、debugger
- **持续改进**：关注技术发展趋势，定期评估技术栈，优化开发流程，提升代码质量

---

## 10. 项目开发环境配置

```bash
# 项目启动
pnpm dev

# 代码检查
pnpm lint

# 构建生产版本
pnpm build

# 代码格式化
pnpm prettier
```

---

## 11. 响应模式与协作原则

- **问题分析**：深入理解需求，分析技术可行性，提供多种解决方案，评估优缺点
- **代码实现**：提供完整可运行代码，包含类型定义，详细中文注释，遵循项目规范
- **问题解决**：快速定位问题根源，提供针对性解决方案，预防类似问题再次发生，更新相关文档
- **知识分享**：解释技术原理，分享最佳实践，推荐学习资源，提供代码示例
- **沟通方式**：使用中文简体，术语准确，方案具体可操作，及时反馈进度
- **代码审查**：关注质量与安全，优化建议，符合规范，注重性能
- **持续改进**：关注新技术，优化流程，提升质量

---

## 操作按钮与表单弹窗调优补充规则

### 1. 操作按钮 type 类型规范

- **编辑**：`type="success"`
- **详情/查看**：`type="default"`
- **新增、导入、导出、保存等主要操作**：`type="primary"`
- **删除、批量删除、危险操作**：`type="warning"`
- 其他特殊操作可根据实际业务自定义 type

### 2. 操作按钮过多时的下拉展示

- 当操作按钮数量较多时，主操作按钮保留常用操作，其余操作通过“更多”按钮（`n-dropdown`）下拉展示，提升页面整洁性和可用性。
- 示例用法：
  ```vue
  <n-space>
    <n-button type="success">编辑</n-button>
    <n-button type="default">详情</n-button>
    <n-dropdown :options="moreOptions" @select="handleMore">
      <n-button type="primary">更多</n-button>
    </n-dropdown>
  </n-space>
  ```

### 3. 表单弹窗统一使用 alert-content 包裹

- 所有表单类弹窗页面，必须使用 `<alert-content>` 组件包裹，统一弹窗交互和按钮风格。
- 推荐用法（参考 internal-file-form.vue）：
  ```vue
  <alert-content :on-save="handleSave" :on-cancel="handleCancel">
    <n-form ...> ... </n-form>
  </alert-content>
  ```
- 可通过 `buttons` 属性自定义按钮文本、类型、显示/隐藏等，详见 `docs/alert-content.md`。

### 4. n-form 校验规则

- n-form 必须配置 `rules`，根据需求文档和原型（如墨刀）综合判断哪些字段必填、格式校验、长度限制等。
- 示例：
  ```js
  const rules = {
    name: { required: true, message: '请输入机构名称', trigger: 'blur' },
    code: { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
    // 其他字段...
  }
  ```

### 5. Vue 全局加载说明

- 不需要在每个页面/组件中 `import { ref, computed, ... } from 'vue'`，这些已全局自动加载，直接使用即可。

---

> 具体 alert-content 用法、按钮自定义、footer插槽、按钮类型等详见 `docs/alert-content.md`。
> 以上规范适用于所有表单弹窗、操作按钮、批量操作等场景，确保交互一致、代码规范、易于维护。

--- 



> **说明**：本规则为 Vue3 + Vite 前端项目专用补充，建议与通用 AI 规则合并使用，确保团队协作和代码质量。
> 本配置文件将根据项目发展和团队需求持续更新。 