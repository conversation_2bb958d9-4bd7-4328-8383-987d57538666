<template>
   <n-drawer v-model:show="active" :width="502" placement="right" >
    <n-drawer-content title="调试工具" closable>
        <n-card title="公共文件上传" size="small">
            <minio-upload   v-model:file-list="fileList" :upload-props="{ multiple: true, showFileList: false }">
                <template #drag>
                        <div class="w-full h-full flex-v justify-center items-center p-10px">
                            <img :src="uploadFileImg" class="w-40px h-40px mt-10px" />
                            <span class="text-14px mt-10px">点击或者拖动文件到该区域来上传</span>
                        </div>
                    </template>
            </minio-upload>
            <div v-for="item in fileList" :key="item.response?.id" class="flex justify-between items-center p-5px">
                <span class="flex-0.6">{{ item.response?.name }}</span>
                <span class="flex-0.4 ml-10px min-w-160px">{{ item.response?.id }}</span>
                <n-button text type="success" @click="copyText(item.response?.id)">复制</n-button>
            </div>
        </n-card>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import uploadFileImg from '@/assets/images/file/upload-file.webp';
import copy from 'copy-to-clipboard';

const active = ref(false)
const fileList = ref<any[]>([])

const copyText = async (text: string) => {
    await copy(text);
    window.$message.success('复制成功');
};
const open = () => {
  active.value = true
}

defineExpose({
  open
})
</script>

<style scoped>

</style>