/**
 * SSE连接Hook
 * @param url SSE服务端地址
 * @param onMessage 消息回调
 * @param onError 错误回调
 * @param maxReconnect 最大重连次数，默认10次
 * @param heartbeatTimeout 心跳超时时间(毫秒)，默认30秒
 * @returns
 * - close: 关闭连接方法
 * - status: 连接状态 ('connecting'|'open'|'closed'|'error'|'timeout')
 * - reconnectCount: 当前重连次数
 */
export default function useSSE(
    url: string,
    onMessage: (data: any) => void,
    onError?: (e: Event, reconnectCount?: number) => void,
    maxReconnect = 10,
    heartbeatTimeout = 60000, // 超时时间设为 60 秒 (3个心跳周期)
    heartbeatCheckInterval = 15000 // 检测间隔设为 15 秒
) {
    const status = ref<'connecting' | 'open' | 'closed' | 'error' | 'timeout'>('connecting');
    const reconnectCount = ref(0);
    let eventSource: EventSource | null = null;
    let heartbeatTimer: number | null = null;
    let lastHeartbeatTime = Date.now();

    // 启动心跳检测
    const startHeartbeatCheck = () => {
        if (heartbeatTimer) clearInterval(heartbeatTimer);

        heartbeatTimer = window.setInterval(() => {
            const now = Date.now();
            // 如果超过心跳超时时间没有收到心跳包，认为连接已断开
            if (now - lastHeartbeatTime > heartbeatTimeout) {
                status.value = 'timeout';
                if (onError) onError(new Error('SSE心跳超时，连接可能已断开') as any, reconnectCount.value);

                // 尝试重连
                if (reconnectCount.value < maxReconnect) {
                    reconnectCount.value++;
                    close();
                    connect();
                }
            }
        }, heartbeatCheckInterval); // 每15秒检查一次
    };

    /**
     * 计算重连延迟时间（指数退避策略）
     * 初始 3 秒，之后逐次翻倍，最大不超过 30 秒
     */
    const getReconnectDelay = (attempt: number): number => {
        const baseDelay = 3000; // 初始 3 秒
        const maxDelay = 30000; // 最大 30 秒
        const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
        // 添加随机抖动，避免多客户端同时重连
        return delay + Math.random() * 1000;
    };

    const connect = () => {
        status.value = 'connecting';
        eventSource = new window.EventSource(url, { withCredentials: false });
        lastHeartbeatTime = Date.now(); // 重置心跳时间

        // 连接成功时重置重连次数
        eventSource.onopen = () => {
            reconnectCount.value = 0;
            status.value = 'open';
            startHeartbeatCheck(); // 开始心跳检测
        };

        eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data) as NotificationEvent;

                // 处理心跳包
                if (data.kind === 2) {
                    lastHeartbeatTime = Date.now(); // 更新最后心跳时间
                } else {
                    // 非心跳包消息传给回调
                    onMessage(data);
                }
            } catch {
                onMessage(event.data);
            }
        };

        eventSource.onerror = (e) => {
            status.value = 'error';
            if (onError) onError(e, reconnectCount.value);

            // 重连逻辑
            if (eventSource && eventSource.readyState === 2 && reconnectCount.value < maxReconnect) {
                const delay = getReconnectDelay(reconnectCount.value);
                setTimeout(() => {
                    reconnectCount.value++;
                    close();
                    connect();
                }, delay);
            } else if (reconnectCount.value >= maxReconnect) {
                if (onError) onError(new Error('SSE已超过最大重连次数，连接已终止') as any, reconnectCount.value);
            }
        };
    };

    const close = () => {
        if (heartbeatTimer) {
            clearInterval(heartbeatTimer);
            heartbeatTimer = null;
        }

        if (eventSource) {
            eventSource.close();
            eventSource = null;
            status.value = 'closed';
        }
    };

    // 初始连接
    connect();

    return {
        close,
        status,
        reconnectCount,
    };
}

/**
 * 业务参数类型
 * @interface BusinessParam
 */
export interface BusinessParam {
    /**
     * 消息内容
     * @type {string}
     */
    content: string;

    /**
     * 模块名称
     * @type {string}
     */
    moduleName: string;

    /**
     * 未读通知数量
     * @type {number}
     */
    unreadNotificationsCount: number;

    /**
     * 消息级别
     * @type {number}
     */
    level: number;
}

/**
 * 通知事件数据结构类型
 * @interface NotificationEvent
 */
export interface NotificationEvent {
    /**
     * 事件的唯一ID
     * @type {string}
     */
    id: string;

    /**
     * 事件类型或种类
     * @type {number}
     * 1: 站内消息通知
     * 2: 心跳包
     */
    kind: number;

    /**
     * 接收者ID列表
     * @type {string[]}
     */
    recipientIds: string[];

    /**
     * 创建时间的Unix时间戳 (毫秒)
     * @type {number}
     */
    createdAt: number;

    /**
     * 业务相关的具体参数
     * @type {BusinessParam}
     */
    businessParam: BusinessParam;
}
