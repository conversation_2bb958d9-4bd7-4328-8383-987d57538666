<template>
    <alert-content :on-default-save="handleSave">
        <n-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="80"
            :show-feedback="false"
        >
            <n-grid :cols="24" :x-gap="10" :y-gap="10">
                <n-form-item-gi label="申请人" :span="12">
                    <n-input v-model:value="formData.applicant" placeholder="张三" readonly />
                </n-form-item-gi>

                <n-form-item-gi label="申请日期" :span="12">
                    <n-date-picker
                        v-model:value="formData.applyDate"
                        type="date"
                        format="yyyy-MM-dd"
                        placeholder="2025-05-02"
                        disabled
                        class="w-full"
                    />
                </n-form-item-gi>

                <n-form-item-gi label="借阅日期" path="borrowPeriod" :span="24">
                    <n-date-picker
                        v-model:value="formData.borrowPeriod"
                        type="daterange"
                        format="yyyy-MM-dd"
                        placeholder="开始日期-结束日期"
                        class="w-full"
                    />
                </n-form-item-gi>

                <n-form-item-gi label="借阅原因" path="borrowReason" :span="24">
                    <n-select
                        v-model:value="formData.borrowReason"
                        :options="borrowReasonOptions"
                        placeholder="请选择"
                        class="w-200px"
                    />
                    <n-input
                        v-if="formData.borrowReason === 'other'"
                        v-model:value="formData.otherReason"
                        placeholder="请输入其他原因"
                        class="ml-10px flex-1"
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="24" path="distributeList">
                    <div class="flex-v w-100%">
                        <div class
                        >

                            <span class="ml-2px mb-10px required-field">借阅清单</span>
                            <n-button type="primary" size="small" @click="handleAddFile">添加文件</n-button>

                        </div>
                        <vxe-table
                            show-overflow
                            :edit-config="{
                                trigger: 'click',
                                mode: 'cell'
                            }"
                            :data="formData.fileList"
                        >
                            <vxe-column type="seq" title="序号" width="70"></vxe-column>
                            <vxe-column
                                field="status"
                                title="文件有效性"
                                width="120"
                                :edit-render="{ name: 'VxeSelect', options: statusOptions, placeholder: '请点击选择' }"
                            ></vxe-column>
                            <vxe-column
                                field="fileType"
                                title="文件类型"
                                width="120"
                                :edit-render="{
                                    name: 'VxeSelect',
                                    options: typeOptions,
                                    placeholder: '请点击选择',
                                    events: { change: handleFileTypeChange }
                                }"
                            ></vxe-column>
                            <vxe-column
                                field="fileCategory"
                                title="文件类别"
                                width="120"
                                :edit-render="{
                                    name: 'VxeSelect',
                                    options: categoryOptions,
                                    placeholder: '请点击选择',
                                    events: { change: handleCategoryChange }
                                }"
                            >
                            </vxe-column>
                            <vxe-column
                                field="fileName"
                                title="文件名称"
                                width="120"
                                :edit-render="{ name: 'VxeSelect', options: statusOptions, placeholder: '请点击选择' }"
                            ></vxe-column>
                            <vxe-column
                                field="fileNumber"
                                title="文件编号"
                                width="120"
                                :edit-render="{ name: 'VxeSelect', options: statusOptions, placeholder: '请点击选择' }"
                            ></vxe-column>
                            <vxe-column
                                field="fileVersion"
                                title="版本/版次"
                                width="120"
                                :edit-render="{ name: 'VxeSelect', options: statusOptions, placeholder: '请点击选择' }"
                            ></vxe-column>
                            <vxe-column field="todo" title="操作" width="120">
                                <template v-slot="{ row }">
                                    <n-button size="tiny" type="error" @click="handleRemoveFile(row)">删除</n-button>
                                </template>
                            </vxe-column>
                        </vxe-table>
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>

        <div class="mt-20px">
            <div class="flex items-center justify-between mb-10px">
                <span class="text-16px font-600">借阅清单</span>
            </div>
        </div>
    </alert-content>
</template>

<script setup lang="ts">
import { NButton, NInput, NSelect, type FormRules } from 'naive-ui';
import { BorrowingApplicationForm } from '@/api/apis/nebula/api/v1/borrowing-application';
import { VxeTable, VxeColumn } from 'vxe-table';

const props = defineProps<{
    row?: BorrowingApplicationForm;
}>();

const emit = defineEmits<{
    (e: 'submit'): void;
}>();

const formRef = ref();

// 表单数据
const formData = ref<BorrowingApplicationForm>({
    applicant: '张三',
    applyDate: Date.now(),
    borrowPeriod: null,
    borrowReason: '',
    otherReason: '',
    fileList: [
        {
            id: '1',
            fileType: '',
            fileCategory: '',
            fileName: '',
            fileNumber: '',
            fileVersion: '',
            status: ''
        },
        {
            id: '2',
            fileType: '',
            fileCategory: '',
            fileName: '',
            fileNumber: '',
            fileVersion: '',
            status: ''
        }
    ]
});

// 借阅原因选项
const borrowReasonOptions = [
    { label: '工作需要', value: 'work' },
    { label: '学习研究', value: 'study' },
    { label: '项目参考', value: 'project' },
    { label: '其他', value: 'other' }
];

// 表单验证规则
const rules: FormRules = {
    borrowPeriod: [{ required: true, message: '请输入借阅日期', trigger: 'blur' }],
    borrowReason: [{ required: true, message: '请选择借阅原因', trigger: 'change' }]
};

// 添加文件
const handleAddFile = () => {
    formData.value.fileList?.push({
        id: String(Date.now()),
        fileType: '',
        fileCategory: '',
        fileName: '',
        fileNumber: '',
        fileVersion: '',
        status: ''
    });
};

// 删除文件
const handleRemoveFile = (index: number) => {
    formData.value.fileList?.splice(index, 1);
};

// 状态选项
const statusOptions = [
    { label: '有效', value: 'effective' },
    { label: '作废', value: 'cancel' }
];

// 类型选项
const typeOptions = [
    { label: '内部文件', value: 'internal' },
    { label: '外部文件', value: 'external' }
];
const handleFileTypeChange = async (_: any, rowIndex: any) => {
    const dict = await api.sass.api.v1.dict.get('file_business_dictionary');
    let categoryId = '';
    switch (rowIndex.value) {
        case 'internal':
            categoryId = dict.data.data[0].extra.fileCategory.internal;
            break;
        case 'external':
            categoryId = dict.data.data[0].extra.fileCategory.external;
            break;
    }
    if (!categoryId) {
        window.$message.error('获取文件类别树字典数据失败');
        return;
    }
    const res = await $apis.nebula.api.v1.businessDictionary.node.tree({
        id: categoryId
    });
    categoryOptions.value = $utils.treeData.convertTreeData(res.data);
};

// 文件类别
const categoryOptions = ref<Record<string, any>[]>([]);
const handleCategoryChange = async (_: any, rowIndex: any) => {
    console.log(rowIndex.value);
};

// 保存表单
const handleSave = async () => {
    await formRef.value?.validate();

    try {
        // 暂时使用模拟API，实际项目中需要替换为真实API
        if (props.row?.id) {
            // 编辑
            console.log('编辑借阅申请:', formData.value);
            window.$message.success('修改成功');
        } else {
            // 新增
            console.log('新增借阅申请:', formData.value);
            window.$message.success('新增成功');
        }
        emit('submit');
    } catch (error) {
        console.error('保存失败:', error);
    }
};

// 初始化数据
onMounted(() => {
    if (props.row) {
        formData.value = { ...props.row };
    }
});
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
</style>
